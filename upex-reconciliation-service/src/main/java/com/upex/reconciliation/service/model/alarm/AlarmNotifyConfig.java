package com.upex.reconciliation.service.model.alarm;

import com.upex.commons.support.util.ApolloKmsUtils;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.model.config.TemplateConfig;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR>
 * @projectName upex-bill
 * @date 2022/7/22
 */
@Data
public class AlarmNotifyConfig {
    /***是否开启:lark通知***/
    private boolean sendLarkOpen = true;
    /***关闭lark通知业务线***/
    private List<String> sendLarkOffAccountType = new ArrayList<>();
    /***关闭lark通知业务线***/
    private List<String> sendLarkOffTemplateCode = new ArrayList<>();
    /***lark通知地址***/
    private String sendLarkApi;
    /***发送lark失败执行次数***/
    private Integer sendLarkRetryCount = 3;
    /***发送lark失败重试间隔***/
    private Long sendLarkRetryInterval = 1000L;
    /***发送lark密钥***/
    private String sendLarkSecret = "";
    /***场景模板***/
    private TemplateConfig templateConfig = new TemplateConfig();
    /***通知配置***/
    private Map<String, List<String>> atUserNotifyMap = new HashMap<>();
    /***默认@人员列表***/
    private List<String> defaultAtUserList = new ArrayList<>();
    /***模板级别的默认告警配置 key:templateCode, value:是否使用默认告警人员***/
    private Map<String, Boolean> templateUseDefaultAtUserMap = new HashMap<>();
    /***阈值规则配置***/
    private Map<String, Map<String, LinkedHashMap<String, String>>> thresholdRuleConfig = new HashMap<>();
    /***预发环境通知配置***/
    private Map<String, String> preNotifyConfig = new HashMap<>();
    /***最大报警信息长度***/
    private int maxMessageLength = 3000;
    /***userPropertyMap超过xxx，发送提醒消息***/
    private int userPropertyMapAlarmSize = 30000;
    /***延迟消息告警开关，发送提醒消息***/
    private boolean alarmTimeSliceDelayMsgOpen = true;
    /***时间片延迟分钟数 默认20分钟***/
    private Long alarmCheckOkTimeDelayTime = 10 * 60 * 1000L;
    /***总账电话告警延迟时间 默认1小时***/
    private Long bizAlarmEmergencyCheckTimeDelayTime = 20 * 60 * 1000L;
    /***时间片延迟分钟数 默认20分钟***/
    private Long alarmLegerCheckOkTimeDelayTime = 20 * 60 * 1000L;
    /***总账电话告警延迟时间 默认1小时***/
    private Long ledgerAlarmEmergencyCheckTimeDelayTime = 30 * 60 * 1000L;
    /***是否开启电话告警***/
    private boolean twentyFourHourAlarmOpen = true;
    /***时间片延迟分钟数 默认20分钟***/
    private Long alarmErrorMapDelayTime = 20 * 60 * 1000L;
    /***xxljob执行延迟检测时间 格式参考 TimeUnitEnum***/
    private Map<String, String> xxlJobExecuteDelayTimeMap = new HashMap<>();
    /***监控配置开关***/
    private Map<String, Boolean> watchBillCheckConfigFlagMap = new HashMap<>();
    /***监控告警存盘黑名单***/
    private List<String> alarmSaveBlackList = new ArrayList<>();

    public String getSendLarkSecret() {
        return ApolloKmsUtils.decrypt(sendLarkSecret, BillConstants.UPEX_BILL_JOB_NAME);
    }
}
