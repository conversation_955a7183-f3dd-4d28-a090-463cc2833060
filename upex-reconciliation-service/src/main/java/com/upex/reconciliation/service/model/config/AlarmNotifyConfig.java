package com.upex.reconciliation.service.model.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @projectName upex-bill
 * @date 2022/7/22
 * @deprecated
 */
@Data
public class AlarmNotifyConfig {
    /**
     * 是否开启:lark通知
     */
    private boolean sendLarkOpen = false;
    /**
     * lark通知地址
     */
    private String sendLarkApi;
    /**
     * 发送lark失败执行次数
     */
    private Integer sendLarkRetryCount = 3;
    /**
     * 发送lark失败重试间隔
     */
    private Long sendLarkRetryInterval = 1000L;
    /**
     * 发送lark密钥
     */
    private String sendLarkSecret = "";
    /**
     * 负值资产最多可以通知账户数据
     */
    private Integer maxAssetsNegativeUserNum = 10;
    /**
     * 场景模板
     */
    private TemplateConfig templateConfig = new TemplateConfig();
    /**
     * 通知配置
     */
    private Map<String, List<String>> atUserNotifyMap = new HashMap<>();
    /**
     * 默认@人员列表
     */
    private List<String> defaultAtUserList = new ArrayList<>();
    /**
     * 模板级别的默认告警配置 key:templateCode, value:是否使用默认告警人员
     */
    private Map<String, Boolean> templateUseDefaultAtUserMap = new HashMap<>();
    /**
     * 是否开启:阈值报警
     */
    private boolean thresholdAlarmOpen = false;
    /**
     * 阈值规则配置
     */
    private Map<String, Map<String, String>> thresholdRuleConfig = new HashMap<>();
    /**
     * 堆栈跟踪信息长度
     */
    private int stacktraceLength = 3000;
    /**
     * 预发环境通知配置
     */
    private Map<String, String> preNotifyConfig = new HashMap<>();

    public String getSendLarkSecret() {
        return "";
    }
}
