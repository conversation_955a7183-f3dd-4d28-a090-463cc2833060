package com.upex.reconciliation.service.dao.entity;

import lombok.*;

import java.util.Date;

/**
 * 系统对账配置表
 *
 * <AUTHOR>
 * @date 2020-05-11 11:11:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AlarmNotifyTemplate {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 模板编码
     */
    private String templateCode;

    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 模板内容
     */
    private String templateContent;
    /**
     * 版本
     */
    private Integer version;
    /**
     * 是否使用默认告警人员 0-否 1-是
     */
    private Integer useDefaultAtUser;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
}